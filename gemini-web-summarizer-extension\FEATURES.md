# 🚀 新功能说明

## 📋 功能概览

我已经为Gemini Web Summarizer扩展添加了强大的**模型选择功能**和**提示词编辑功能**，让您能够完全自定义AI的行为和输出风格。

## 🤖 模型选择功能

### 支持的模型
- **Gemini 2.5 Flash** (推荐) - 平衡速度和质量
- **Gemini 2.5 Flash 8B** - 最快响应速度
- **Gemini 2.5 Pro** - 最高质量输出
- **Gemini 1.5 Flash** - 经典快速模型
- **Gemini 1.5 Pro** - 经典高质量模型

### 快速模型切换
- **弹出窗口快速选择**: 在扩展弹出窗口顶部可以快速切换模型和总结长度
- **设置页面详细配置**: 在设置页面可以设置默认模型和高级参数

### 思考模式控制
- **启用/禁用思考模式**: 控制AI是否使用思考过程
- **思考预算设置**: 控制思考过程的token消耗量
- **性能优化**: 可根据需要平衡质量和速度

## 📝 提示词编辑功能

### 预设模板

#### 总结模板
1. **默认模板** - 通用的智能总结
2. **学术风格** - 适合学术论文和研究内容
3. **商务风格** - 适合商业报告和市场分析
4. **轻松风格** - 适合日常阅读和娱乐内容
5. **技术风格** - 适合技术文档和开发资料

#### 流程图模板
1. **默认模板** - 标准流程图生成
2. **详细流程** - 包含子步骤和异常处理
3. **简化流程** - 突出核心步骤
4. **决策流程** - 重点展示决策点和分支

### 自定义提示词

#### 变量支持
- `{title}` - 网页标题
- `{url}` - 网页URL
- `{content}` - 网页内容
- `{length}` - 总结长度要求

#### 示例自定义提示词
```
请以专业的角度分析以下{title}的内容：

1. 核心观点提取
2. 逻辑结构分析
3. 关键信息总结
4. 实用价值评估

网页内容：{content}

请生成{length}的专业分析报告。
```

## ⚙️ 使用方法

### 快速设置
1. 点击扩展图标打开弹出窗口
2. 在顶部快速设置栏选择模型和长度
3. 设置会自动保存并应用

### 详细配置
1. 点击设置按钮进入设置页面
2. 在"模型设置"部分：
   - 选择默认模型
   - 配置思考模式
   - 调整思考预算
3. 在"提示词设置"部分：
   - 选择预设模板或自定义
   - 编辑自定义提示词
   - 测试提示词效果

### 提示词测试
1. 在设置页面编辑提示词后
2. 点击"🧪 测试提示词"按钮
3. 系统会使用测试数据验证提示词
4. 查看控制台获取详细测试结果

## 🎯 高级功能

### 动态API调用
- 根据选择的模型动态调用对应的API端点
- 自动优化API参数以匹配模型特性
- 智能错误处理和重试机制

### 设置同步
- 快速设置和详细设置实时同步
- 跨标签页设置共享
- 设置导入导出支持

### 性能优化
- 思考模式可选，平衡质量和速度
- 模型选择优化，根据需求选择最适合的模型
- 缓存机制减少重复API调用

## 📊 模型对比

| 模型 | 速度 | 质量 | 成本 | 适用场景 |
|------|------|------|------|----------|
| Gemini 2.5 Flash | ⭐⭐⭐⭐ | ⭐⭐⭐⭐ | ⭐⭐⭐ | 日常使用，推荐 |
| Gemini 2.5 Flash 8B | ⭐⭐⭐⭐⭐ | ⭐⭐⭐ | ⭐⭐⭐⭐ | 快速总结 |
| Gemini 2.5 Pro | ⭐⭐⭐ | ⭐⭐⭐⭐⭐ | ⭐⭐ | 高质量分析 |
| Gemini 1.5 Flash | ⭐⭐⭐⭐ | ⭐⭐⭐ | ⭐⭐⭐ | 兼容性好 |
| Gemini 1.5 Pro | ⭐⭐⭐ | ⭐⭐⭐⭐ | ⭐⭐ | 稳定可靠 |

## 🔧 配置建议

### 日常使用
- **模型**: Gemini 2.5 Flash
- **思考模式**: 关闭
- **总结长度**: 中等
- **提示词**: 默认模板

### 学术研究
- **模型**: Gemini 2.5 Pro
- **思考模式**: 开启
- **总结长度**: 详细
- **提示词**: 学术风格模板

### 快速浏览
- **模型**: Gemini 2.5 Flash 8B
- **思考模式**: 关闭
- **总结长度**: 简短
- **提示词**: 轻松风格模板

### 技术分析
- **模型**: Gemini 2.5 Pro
- **思考模式**: 开启
- **总结长度**: 详细
- **提示词**: 技术风格模板

## 🚀 使用技巧

### 提示词优化
1. **明确目标**: 在提示词中明确说明期望的输出格式
2. **使用变量**: 充分利用 `{title}`, `{content}` 等变量
3. **分步骤**: 将复杂任务分解为多个步骤
4. **设置约束**: 明确长度、风格、语言等要求

### 模型选择策略
1. **内容复杂度**: 复杂内容选择Pro模型，简单内容选择Flash模型
2. **响应时间**: 需要快速响应时选择8B模型
3. **质量要求**: 高质量要求时启用思考模式
4. **成本控制**: 日常使用建议关闭思考模式

### 性能优化
1. **合理设置思考预算**: 根据内容复杂度调整
2. **选择合适的输出长度**: 避免不必要的长输出
3. **使用缓存**: 相同内容避免重复处理
4. **批量处理**: 多个页面可以分批处理

## 🔄 更新说明

### v1.1.0 新增功能
- ✅ 多模型支持 (5种Gemini模型)
- ✅ 思考模式控制
- ✅ 提示词模板系统 (9种预设模板)
- ✅ 自定义提示词编辑
- ✅ 快速设置栏
- ✅ 提示词测试功能
- ✅ 动态API调用
- ✅ 设置同步优化

### 兼容性
- 完全向后兼容v1.0.0设置
- 自动迁移旧版本配置
- 保持原有功能不变

---

🎉 现在您可以享受更强大、更灵活的AI网页总结体验！
