# 安装和使用指南

## 快速开始

### 1. 准备工作

#### 获取Gemini API密钥
1. 访问 [Google AI Studio](https://makersuite.google.com/app/apikey)
2. 使用Google账户登录
3. 点击"Create API Key"创建新的API密钥
4. 复制生成的API密钥（格式类似：AIzaSyXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX）

### 2. 安装扩展

#### 方法一：开发者模式安装（推荐）
1. 下载项目文件到本地
2. 打开Chrome浏览器
3. 在地址栏输入 `chrome://extensions/` 并回车
4. 开启右上角的"开发者模式"开关
5. 点击"加载已解压的扩展程序"
6. 选择项目文件夹 `gemini-web-summarizer-extension`
7. 扩展安装完成，会在工具栏显示🤖图标

#### 方法二：打包安装
1. 在扩展管理页面点击"打包扩展程序"
2. 选择项目文件夹，生成.crx文件
3. 将.crx文件拖拽到扩展管理页面进行安装

### 3. 生成图标文件

由于图标文件需要单独生成，请按以下步骤操作：

1. 在浏览器中打开项目根目录下的 `create-icons.html` 文件
2. 点击"生成图标"按钮
3. 右键点击生成的每个图标，选择"图片另存为"
4. 将图标保存到 `icons/` 文件夹中：
   - 16x16图标保存为 `icon16.png`
   - 48x48图标保存为 `icon48.png`
   - 128x128图标保存为 `icon128.png`
5. 刷新扩展或重新加载扩展

### 4. 配置设置

1. 点击扩展图标，在弹出窗口中点击"⚙️"设置按钮
2. 或者右键点击扩展图标，选择"选项"
3. 在API设置中输入您的Gemini API密钥
4. 点击"🔍 验证API密钥"确保密钥有效
5. 根据需要调整其他设置：
   - 启用/禁用扩展功能
   - 设置总结长度
   - 调整AI创造性参数
6. 点击"💾 保存设置"

## 使用方法

### 网页总结功能

1. **打开目标网页**
   - 访问任何包含文本内容的网页
   - 确保页面已完全加载

2. **生成总结**
   - 点击浏览器工具栏中的🤖扩展图标
   - 在弹出窗口中点击"📄 总结当前页面"
   - 等待AI处理（通常需要5-15秒）
   - 查看生成的总结结果

3. **复制总结**
   - 点击总结结果右上角的"📋"复制按钮
   - 总结内容将复制到剪贴板
   - 可以粘贴到任何地方使用

### 流程图生成功能

1. **选择合适的网页**
   - 选择包含流程、步骤或过程描述的网页
   - 如：教程、操作指南、工作流程等

2. **生成流程图**
   - 点击扩展图标打开弹出窗口
   - 切换到"📊 流程图"标签页
   - 点击"🔄 生成流程图"按钮
   - 等待AI分析并生成Mermaid流程图

3. **图表交互**
   - **缩放**：使用🔍+和🔍-按钮，或鼠标滚轮
   - **拖拽**：按住鼠标左键拖动图表
   - **重置**：点击🔄按钮恢复默认视图
   - **下载**：点击📥PNG或📥SVG按钮下载图表

## 高级设置

### API参数调整

1. **Temperature（创造性）**
   - 范围：0.0 - 1.0
   - 低值（0.1-0.3）：更一致、准确的结果
   - 中值（0.4-0.7）：平衡创造性和准确性
   - 高值（0.8-1.0）：更有创意但可能不够准确

2. **最大输出长度**
   - 1024 tokens：适合简短总结
   - 2048 tokens：标准长度（推荐）
   - 4096 tokens：详细总结

3. **总结长度设置**
   - 简短：100-200字
   - 中等：300-500字
   - 详细：500-800字

### 数据管理

1. **导出设置**
   - 在设置页面点击"📤 导出设置"
   - 下载包含所有配置的JSON文件
   - 可用于备份或在其他设备上恢复设置

2. **导入设置**
   - 点击"📥 导入设置"
   - 选择之前导出的JSON文件
   - 自动恢复所有配置

3. **重置设置**
   - 点击"🔄 重置设置"
   - 确认后恢复所有默认设置
   - 清除使用统计数据

## 故障排除

### 常见问题

**Q: 扩展图标不显示或显示为灰色**
A: 
- 检查是否正确安装了图标文件
- 确保图标文件名正确（icon16.png, icon48.png, icon128.png）
- 尝试重新加载扩展

**Q: API密钥验证失败**
A:
- 确认API密钥格式正确（以AIzaSy开头）
- 检查API密钥是否有效且未过期
- 确认网络连接正常
- 检查是否有防火墙阻止API访问

**Q: 无法获取页面内容**
A:
- 确保不是在系统页面（chrome://等）
- 刷新页面后重试
- 检查页面是否完全加载
- 某些网站可能有内容保护机制

**Q: 流程图无法显示**
A:
- 检查网络连接（需要加载Mermaid库）
- 确认生成的代码格式正确
- 尝试重新生成流程图
- 查看浏览器控制台的错误信息

**Q: 总结结果不准确**
A:
- 尝试调整Temperature参数
- 选择更合适的总结长度
- 确保页面内容质量良好
- 考虑手动编辑总结结果

### 调试模式

1. 打开Chrome开发者工具（F12）
2. 查看Console面板的错误信息
3. 检查Network面板的API请求状态
4. 在扩展管理页面查看扩展详情和错误

### 性能优化

1. **减少API调用**
   - 避免频繁重复总结同一页面
   - 合理设置最大输出长度

2. **网络优化**
   - 确保稳定的网络连接
   - 考虑使用VPN如果API访问受限

3. **内存管理**
   - 定期清理浏览器缓存
   - 关闭不必要的标签页

## 更新和维护

### 扩展更新
- 开发者模式安装的扩展需要手动更新
- 下载新版本文件替换原文件
- 在扩展管理页面点击"重新加载"

### 数据备份
- 定期导出设置进行备份
- 重要的总结结果建议保存到本地文件

### 版本兼容性
- 支持Chrome 88+版本
- 使用Manifest V3规范
- 兼容最新的Gemini API

## 技术支持

如果遇到问题，请：
1. 查看本指南的故障排除部分
2. 检查浏览器控制台的错误信息
3. 确认API密钥和网络连接正常
4. 尝试重新安装扩展

---

🎉 现在您可以开始使用Gemini Web Summarizer扩展了！
