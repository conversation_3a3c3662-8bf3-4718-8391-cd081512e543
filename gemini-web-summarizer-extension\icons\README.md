# 图标文件夹

此文件夹应包含以下图标文件：

- `icon16.png` - 16x16像素，用于扩展工具栏
- `icon48.png` - 48x48像素，用于扩展管理页面
- `icon128.png` - 128x128像素，用于Chrome网上应用店

## 生成图标

1. 在浏览器中打开项目根目录下的 `create-icons.html` 文件
2. 点击"生成图标"按钮
3. 右键点击生成的图标，选择"图片另存为"
4. 将图标保存到此文件夹中，使用对应的文件名

## 图标设计

图标采用渐变蓝紫色背景，中央显示机器人emoji (🤖)，体现AI智能助手的特性。

设计元素：
- 背景：线性渐变 (#667eea → #764ba2)
- 图标：白色机器人emoji
- 圆角：15%的圆角半径
- 阴影：适度的投影效果

## 替代方案

如果无法使用HTML生成器，您也可以：

1. 使用任何图像编辑软件（如Photoshop、GIMP、Canva等）
2. 创建对应尺寸的图像
3. 使用相同的设计风格和颜色
4. 保存为PNG格式并放置在此文件夹中
