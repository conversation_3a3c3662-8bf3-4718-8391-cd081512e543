/**
 * Gemini API 封装库
 * 提供与Google Gemini API交互的功能
 */

// Gemini API配置
const GEMINI_API_CONFIG = {
    baseUrl: 'https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash:generateContent',
    maxRetries: 3,
    retryDelay: 1000,
    timeout: 30000
};

/**
 * 调用Gemini API
 * @param {string} apiKey - API密钥
 * @param {string} prompt - 提示词
 * @param {Object} options - 可选配置
 * @returns {Promise<string>} API响应内容
 */
async function callGeminiAPI(apiKey, prompt, options = {}) {
    if (!apiKey) {
        throw new Error('API密钥不能为空');
    }
    
    if (!prompt) {
        throw new Error('提示词不能为空');
    }
    
    const requestBody = {
        contents: [{
            parts: [{
                text: prompt
            }]
        }],
        generationConfig: {
            temperature: options.temperature || 0.7,
            topK: options.topK || 40,
            topP: options.topP || 0.95,
            maxOutputTokens: options.maxOutputTokens || 2048,
            stopSequences: options.stopSequences || [],
            // 思考配置
            thinkingConfig: {
                thinkingBudget: options.thinkingBudget !== undefined ? options.thinkingBudget : 0
            }
        },
        safetySettings: [
            {
                category: "HARM_CATEGORY_HARASSMENT",
                threshold: "BLOCK_MEDIUM_AND_ABOVE"
            },
            {
                category: "HARM_CATEGORY_HATE_SPEECH",
                threshold: "BLOCK_MEDIUM_AND_ABOVE"
            },
            {
                category: "HARM_CATEGORY_SEXUALLY_EXPLICIT",
                threshold: "BLOCK_MEDIUM_AND_ABOVE"
            },
            {
                category: "HARM_CATEGORY_DANGEROUS_CONTENT",
                threshold: "BLOCK_MEDIUM_AND_ABOVE"
            }
        ]
    };
    
    // 支持动态模型URL
    const baseUrl = options._modelUrl || GEMINI_API_CONFIG.baseUrl;
    const url = `${baseUrl}?key=${apiKey}`;
    
    let lastError;
    for (let attempt = 1; attempt <= GEMINI_API_CONFIG.maxRetries; attempt++) {
        try {
            const response = await fetchWithTimeout(url, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(requestBody)
            }, GEMINI_API_CONFIG.timeout);
            
            if (!response.ok) {
                const errorData = await response.json().catch(() => ({}));
                throw new APIError(
                    `API请求失败: ${response.status} ${response.statusText}`,
                    response.status,
                    errorData
                );
            }
            
            const data = await response.json();
            
            if (!data.candidates || data.candidates.length === 0) {
                throw new Error('API返回数据格式错误：没有候选结果');
            }
            
            const candidate = data.candidates[0];
            if (!candidate.content || !candidate.content.parts || candidate.content.parts.length === 0) {
                throw new Error('API返回数据格式错误：没有内容部分');
            }
            
            return candidate.content.parts[0].text;
            
        } catch (error) {
            lastError = error;
            console.error(`API调用失败 (尝试 ${attempt}/${GEMINI_API_CONFIG.maxRetries}):`, error);
            
            // 如果是最后一次尝试，或者是不可重试的错误，直接抛出
            if (attempt === GEMINI_API_CONFIG.maxRetries || !isRetryableError(error)) {
                break;
            }
            
            // 等待后重试
            await sleep(GEMINI_API_CONFIG.retryDelay * attempt);
        }
    }
    
    throw lastError;
}

/**
 * 验证API密钥
 * @param {string} apiKey - 要验证的API密钥
 * @returns {Promise<boolean>} 验证结果
 */
async function validateApiKey(apiKey) {
    try {
        // 使用简单的测试请求验证API密钥
        const testPrompt = 'Hello';
        const result = await callGeminiAPI(apiKey, testPrompt, {
            maxOutputTokens: 10,
            temperature: 0.1
        });

        // 如果能获得响应，说明API密钥有效
        return result && result.length > 0;
    } catch (error) {
        console.error('API密钥验证失败:', error);

        // 检查具体的错误类型
        if (error instanceof APIError) {
            // 401表示API密钥无效，403表示权限不足
            if (error.status === 401 || error.status === 403) {
                return false;
            }
            // 其他错误可能是网络问题，不一定是API密钥问题
            if (error.status >= 500) {
                throw new Error('服务器错误，请稍后重试');
            }
        }

        return false;
    }
}

/**
 * 带超时的fetch请求
 * @param {string} url - 请求URL
 * @param {Object} options - fetch选项
 * @param {number} timeout - 超时时间（毫秒）
 * @returns {Promise<Response>} fetch响应
 */
function fetchWithTimeout(url, options, timeout) {
    return new Promise((resolve, reject) => {
        const controller = new AbortController();
        const timeoutId = setTimeout(() => {
            controller.abort();
            reject(new Error('请求超时'));
        }, timeout);
        
        fetch(url, {
            ...options,
            signal: controller.signal
        })
        .then(response => {
            clearTimeout(timeoutId);
            resolve(response);
        })
        .catch(error => {
            clearTimeout(timeoutId);
            if (error.name === 'AbortError') {
                reject(new Error('请求超时'));
            } else {
                reject(error);
            }
        });
    });
}

/**
 * 判断错误是否可重试
 * @param {Error} error - 错误对象
 * @returns {boolean} 是否可重试
 */
function isRetryableError(error) {
    // 网络错误通常可以重试
    if (error.name === 'TypeError' && error.message.includes('fetch')) {
        return true;
    }
    
    // 超时错误可以重试
    if (error.message.includes('超时') || error.message.includes('timeout')) {
        return true;
    }
    
    // API错误根据状态码判断
    if (error instanceof APIError) {
        // 5xx服务器错误可以重试
        if (error.status >= 500 && error.status < 600) {
            return true;
        }
        
        // 429限流错误可以重试
        if (error.status === 429) {
            return true;
        }
        
        // 4xx客户端错误通常不可重试
        return false;
    }
    
    return false;
}

/**
 * 睡眠函数
 * @param {number} ms - 睡眠时间（毫秒）
 * @returns {Promise<void>}
 */
function sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
}

/**
 * API错误类
 */
class APIError extends Error {
    constructor(message, status, data) {
        super(message);
        this.name = 'APIError';
        this.status = status;
        this.data = data;
    }
}

/**
 * 格式化API错误消息
 * @param {Error} error - 错误对象
 * @returns {string} 格式化的错误消息
 */
function formatApiError(error) {
    if (error instanceof APIError) {
        switch (error.status) {
            case 400:
                return 'API请求参数错误，请检查输入内容';
            case 401:
                return 'API密钥无效，请检查密钥设置';
            case 403:
                return 'API访问被拒绝，请检查密钥权限';
            case 429:
                return 'API调用频率过高，请稍后重试';
            case 500:
                return 'API服务器内部错误，请稍后重试';
            case 503:
                return 'API服务暂时不可用，请稍后重试';
            default:
                return `API错误 (${error.status}): ${error.message}`;
        }
    }
    
    if (error.message.includes('超时') || error.message.includes('timeout')) {
        return '请求超时，请检查网络连接';
    }
    
    if (error.message.includes('fetch')) {
        return '网络连接失败，请检查网络设置';
    }
    
    return error.message || '未知错误';
}

// 导出函数（在扩展环境中直接使用）
if (typeof window !== 'undefined') {
    window.callGeminiAPI = callGeminiAPI;
    window.validateApiKey = validateApiKey;
    window.formatApiError = formatApiError;
    window.APIError = APIError;
}
