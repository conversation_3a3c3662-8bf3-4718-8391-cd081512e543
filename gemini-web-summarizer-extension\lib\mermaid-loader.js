/**
 * Mermaid 库加载器
 * 动态加载Mermaid库并初始化
 */

// Mermaid CDN配置
const MERMAID_CONFIG = {
    cdnUrl: 'https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js',
    fallbackUrl: 'https://unpkg.com/mermaid@10.6.1/dist/mermaid.min.js',
    version: '10.6.1'
};

/**
 * 加载Mermaid库
 * @returns {Promise<void>}
 */
async function loadMermaid() {
    // 检查是否已经加载
    if (typeof mermaid !== 'undefined') {
        return Promise.resolve();
    }
    
    return new Promise((resolve, reject) => {
        const script = document.createElement('script');
        script.src = MERMAID_CONFIG.cdnUrl;
        
        script.onload = () => {
            try {
                // 初始化Mermaid
                mermaid.initialize({
                    startOnLoad: false,
                    theme: 'default',
                    securityLevel: 'loose',
                    fontFamily: 'Arial, sans-serif',
                    fontSize: 14,
                    flowchart: {
                        useMaxWidth: true,
                        htmlLabels: true,
                        curve: 'basis'
                    },
                    sequence: {
                        diagramMarginX: 50,
                        diagramMarginY: 10,
                        actorMargin: 50,
                        width: 150,
                        height: 65,
                        boxMargin: 10,
                        boxTextMargin: 5,
                        noteMargin: 10,
                        messageMargin: 35
                    },
                    gantt: {
                        titleTopMargin: 25,
                        barHeight: 20,
                        fontFamily: 'Arial, sans-serif',
                        fontSize: 11,
                        gridLineStartPadding: 35,
                        bottomPadding: 25,
                        leftPadding: 75,
                        rightPadding: 50
                    }
                });
                
                console.log('Mermaid库加载成功');
                resolve();
            } catch (error) {
                console.error('Mermaid初始化失败:', error);
                reject(error);
            }
        };
        
        script.onerror = () => {
            console.warn('主CDN加载失败，尝试备用CDN');
            
            // 尝试备用CDN
            const fallbackScript = document.createElement('script');
            fallbackScript.src = MERMAID_CONFIG.fallbackUrl;
            
            fallbackScript.onload = () => {
                try {
                    mermaid.initialize({
                        startOnLoad: false,
                        theme: 'default',
                        securityLevel: 'loose'
                    });
                    console.log('Mermaid库从备用CDN加载成功');
                    resolve();
                } catch (error) {
                    console.error('Mermaid初始化失败:', error);
                    reject(error);
                }
            };
            
            fallbackScript.onerror = () => {
                const error = new Error('无法加载Mermaid库，请检查网络连接');
                console.error(error);
                reject(error);
            };
            
            document.head.appendChild(fallbackScript);
        };
        
        document.head.appendChild(script);
    });
}

/**
 * 渲染Mermaid图表
 * @param {string} graphDefinition - Mermaid图表定义
 * @param {string} elementId - 目标元素ID
 * @returns {Promise<string>} 渲染后的SVG字符串
 */
async function renderMermaidChart(graphDefinition, elementId = 'mermaid-chart') {
    try {
        // 确保Mermaid已加载
        if (typeof mermaid === 'undefined') {
            await loadMermaid();
        }
        
        // 清理图表定义
        const cleanDefinition = cleanMermaidDefinition(graphDefinition);
        
        // 生成唯一ID
        const chartId = `mermaid-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
        
        // 渲染图表
        const { svg } = await mermaid.render(chartId, cleanDefinition);
        
        // 将SVG插入到指定元素
        const element = document.getElementById(elementId);
        if (element) {
            element.innerHTML = svg;
        }
        
        return svg;
        
    } catch (error) {
        console.error('Mermaid图表渲染失败:', error);
        throw new Error(`图表渲染失败: ${error.message}`);
    }
}

/**
 * 清理Mermaid图表定义
 * @param {string} definition - 原始图表定义
 * @returns {string} 清理后的图表定义
 */
function cleanMermaidDefinition(definition) {
    return definition
        // 移除代码块标记
        .replace(/```mermaid\n?/g, '')
        .replace(/```\n?/g, '')
        // 移除多余的空行
        .replace(/\n\s*\n/g, '\n')
        // 确保以换行符结尾
        .trim();
}

/**
 * 验证Mermaid图表定义
 * @param {string} definition - 图表定义
 * @returns {boolean} 是否有效
 */
function validateMermaidDefinition(definition) {
    const cleanDef = cleanMermaidDefinition(definition);
    
    // 基本验证
    if (!cleanDef || cleanDef.length < 5) {
        return false;
    }
    
    // 检查是否包含有效的图表类型
    const validTypes = [
        'graph', 'flowchart', 'sequenceDiagram', 'classDiagram',
        'stateDiagram', 'erDiagram', 'journey', 'gantt', 'pie',
        'gitgraph', 'mindmap', 'timeline'
    ];
    
    const hasValidType = validTypes.some(type => 
        cleanDef.toLowerCase().includes(type.toLowerCase())
    );
    
    return hasValidType;
}

/**
 * 获取支持的图表类型
 * @returns {Array<Object>} 图表类型列表
 */
function getSupportedChartTypes() {
    return [
        {
            type: 'flowchart',
            name: '流程图',
            syntax: 'flowchart TD',
            description: '显示流程和决策路径'
        },
        {
            type: 'sequence',
            name: '时序图',
            syntax: 'sequenceDiagram',
            description: '显示对象间的交互序列'
        },
        {
            type: 'class',
            name: '类图',
            syntax: 'classDiagram',
            description: '显示类和它们之间的关系'
        },
        {
            type: 'state',
            name: '状态图',
            syntax: 'stateDiagram-v2',
            description: '显示状态转换'
        },
        {
            type: 'er',
            name: '实体关系图',
            syntax: 'erDiagram',
            description: '显示数据库实体关系'
        },
        {
            type: 'journey',
            name: '用户旅程图',
            syntax: 'journey',
            description: '显示用户体验流程'
        },
        {
            type: 'gantt',
            name: '甘特图',
            syntax: 'gantt',
            description: '显示项目时间线'
        },
        {
            type: 'pie',
            name: '饼图',
            syntax: 'pie title',
            description: '显示数据比例'
        }
    ];
}

/**
 * 生成示例图表
 * @param {string} type - 图表类型
 * @returns {string} 示例图表定义
 */
function generateExampleChart(type) {
    const examples = {
        flowchart: `flowchart TD
    A[开始] --> B{是否满足条件?}
    B -->|是| C[执行操作A]
    B -->|否| D[执行操作B]
    C --> E[结束]
    D --> E`,
        
        sequence: `sequenceDiagram
    participant A as 用户
    participant B as 系统
    A->>B: 发送请求
    B-->>A: 返回响应
    A->>B: 确认收到
    B-->>A: 处理完成`,
        
        class: `classDiagram
    class Animal {
        +String name
        +int age
        +makeSound()
    }
    class Dog {
        +String breed
        +bark()
    }
    Animal <|-- Dog`,
        
        state: `stateDiagram-v2
    [*] --> 待处理
    待处理 --> 处理中: 开始处理
    处理中 --> 已完成: 处理完成
    处理中 --> 失败: 处理失败
    失败 --> 待处理: 重新处理
    已完成 --> [*]`,
        
        pie: `pie title 数据分布
    "类别A" : 42.96
    "类别B" : 50.05
    "类别C" : 10.01
    "其他" : 5`
    };
    
    return examples[type] || examples.flowchart;
}

// 导出函数
if (typeof window !== 'undefined') {
    window.loadMermaid = loadMermaid;
    window.renderMermaidChart = renderMermaidChart;
    window.validateMermaidDefinition = validateMermaidDefinition;
    window.getSupportedChartTypes = getSupportedChartTypes;
    window.generateExampleChart = generateExampleChart;
}
