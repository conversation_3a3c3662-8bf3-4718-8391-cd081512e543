/**
 * 本地Mermaid渲染器 - 避免CSP问题
 * 使用简单的SVG生成来渲染基本的流程图
 */

class SimpleMermaidRenderer {
    constructor() {
        this.nodeWidth = 120;
        this.nodeHeight = 40;
        this.nodeSpacing = 150;
        this.levelSpacing = 100;
        this.nodes = new Map();
        this.edges = [];
        this.levels = new Map();
    }

    // 解析Mermaid代码
    parseMermaidCode(code) {
        console.log('解析Mermaid代码:', code);
        
        this.nodes.clear();
        this.edges = [];
        this.levels.clear();

        const lines = code.split('\n').map(line => line.trim()).filter(line => line && !line.startsWith('flowchart'));
        
        for (const line of lines) {
            this.parseLine(line);
        }

        this.calculateLayout();
        return this.generateSVG();
    }

    // 解析单行
    parseLine(line) {
        // 匹配节点定义和连接
        const connectionMatch = line.match(/(\w+)(\[.*?\])?(\{.*?\})?\s*-->\s*(\w+)(\[.*?\])?(\{.*?\})?(\|.*?\|)?/);
        
        if (connectionMatch) {
            const [, fromId, fromLabel, fromShape, toId, toLabel, toShape, edgeLabel] = connectionMatch;
            
            // 添加节点
            this.addNode(fromId, fromLabel || fromShape, this.getShapeType(fromLabel, fromShape));
            this.addNode(toId, toLabel || toShape, this.getShapeType(toLabel, toShape));
            
            // 添加边
            this.edges.push({
                from: fromId,
                to: toId,
                label: edgeLabel ? edgeLabel.slice(1, -1) : ''
            });
        } else {
            // 尝试匹配单独的节点定义
            const nodeMatch = line.match(/(\w+)(\[.*?\]|\{.*?\})/);
            if (nodeMatch) {
                const [, id, labelWithShape] = nodeMatch;
                this.addNode(id, labelWithShape, this.getShapeType(labelWithShape));
            }
        }
    }

    // 添加节点
    addNode(id, labelWithShape, shapeType) {
        if (!this.nodes.has(id)) {
            let label = id;
            if (labelWithShape) {
                if (labelWithShape.startsWith('[') && labelWithShape.endsWith(']')) {
                    label = labelWithShape.slice(1, -1);
                } else if (labelWithShape.startsWith('{') && labelWithShape.endsWith('}')) {
                    label = labelWithShape.slice(1, -1);
                }
            }
            
            this.nodes.set(id, {
                id,
                label,
                shape: shapeType,
                x: 0,
                y: 0
            });
        }
    }

    // 获取形状类型
    getShapeType(labelWithShape, shapeOnly) {
        const shape = labelWithShape || shapeOnly || '';
        if (shape.startsWith('{') && shape.endsWith('}')) {
            return 'diamond'; // 决策节点
        }
        return 'rect'; // 默认矩形
    }

    // 计算布局
    calculateLayout() {
        // 简单的层次布局算法
        const visited = new Set();
        const levels = [];
        
        // 找到起始节点（没有入边的节点）
        const hasIncoming = new Set();
        this.edges.forEach(edge => hasIncoming.add(edge.to));
        
        const startNodes = Array.from(this.nodes.keys()).filter(id => !hasIncoming.has(id));
        if (startNodes.length === 0 && this.nodes.size > 0) {
            startNodes.push(Array.from(this.nodes.keys())[0]);
        }

        // BFS分层
        let currentLevel = startNodes;
        let levelIndex = 0;
        
        while (currentLevel.length > 0) {
            levels[levelIndex] = [...currentLevel];
            const nextLevel = [];
            
            currentLevel.forEach(nodeId => {
                visited.add(nodeId);
                this.edges.forEach(edge => {
                    if (edge.from === nodeId && !visited.has(edge.to)) {
                        if (!nextLevel.includes(edge.to)) {
                            nextLevel.push(edge.to);
                        }
                    }
                });
            });
            
            currentLevel = nextLevel.filter(id => !visited.has(id));
            levelIndex++;
        }

        // 添加未访问的节点
        this.nodes.forEach((node, id) => {
            if (!visited.has(id)) {
                if (!levels[levelIndex]) levels[levelIndex] = [];
                levels[levelIndex].push(id);
            }
        });

        // 设置坐标
        levels.forEach((level, y) => {
            level.forEach((nodeId, x) => {
                const node = this.nodes.get(nodeId);
                if (node) {
                    node.x = x * this.nodeSpacing + 50;
                    node.y = y * this.levelSpacing + 50;
                }
            });
        });
    }

    // 生成SVG
    generateSVG() {
        const maxX = Math.max(...Array.from(this.nodes.values()).map(n => n.x)) + this.nodeWidth + 50;
        const maxY = Math.max(...Array.from(this.nodes.values()).map(n => n.y)) + this.nodeHeight + 50;
        
        let svg = `<svg width="${maxX}" height="${maxY}" xmlns="http://www.w3.org/2000/svg">`;
        
        // 添加样式
        svg += `
        <defs>
            <style>
                .node-rect { fill: #e1f5fe; stroke: #01579b; stroke-width: 2; }
                .node-diamond { fill: #fff3e0; stroke: #e65100; stroke-width: 2; }
                .node-text { font-family: Arial, sans-serif; font-size: 12px; text-anchor: middle; dominant-baseline: middle; }
                .edge-line { stroke: #424242; stroke-width: 2; marker-end: url(#arrowhead); }
                .edge-text { font-family: Arial, sans-serif; font-size: 10px; text-anchor: middle; fill: #424242; }
            </style>
            <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                <polygon points="0 0, 10 3.5, 0 7" fill="#424242" />
            </marker>
        </defs>`;

        // 绘制边
        this.edges.forEach(edge => {
            const fromNode = this.nodes.get(edge.from);
            const toNode = this.nodes.get(edge.to);
            
            if (fromNode && toNode) {
                const x1 = fromNode.x + this.nodeWidth / 2;
                const y1 = fromNode.y + this.nodeHeight;
                const x2 = toNode.x + this.nodeWidth / 2;
                const y2 = toNode.y;
                
                svg += `<line x1="${x1}" y1="${y1}" x2="${x2}" y2="${y2}" class="edge-line" />`;
                
                if (edge.label) {
                    const midX = (x1 + x2) / 2;
                    const midY = (y1 + y2) / 2;
                    svg += `<text x="${midX}" y="${midY}" class="edge-text">${edge.label}</text>`;
                }
            }
        });

        // 绘制节点
        this.nodes.forEach(node => {
            const centerX = node.x + this.nodeWidth / 2;
            const centerY = node.y + this.nodeHeight / 2;
            
            if (node.shape === 'diamond') {
                // 菱形（决策节点）
                const points = [
                    [centerX, node.y],
                    [node.x + this.nodeWidth, centerY],
                    [centerX, node.y + this.nodeHeight],
                    [node.x, centerY]
                ].map(p => p.join(',')).join(' ');
                
                svg += `<polygon points="${points}" class="node-diamond" />`;
            } else {
                // 矩形
                svg += `<rect x="${node.x}" y="${node.y}" width="${this.nodeWidth}" height="${this.nodeHeight}" class="node-rect" rx="5" />`;
            }
            
            // 节点文本
            svg += `<text x="${centerX}" y="${centerY}" class="node-text">${node.label}</text>`;
        });

        svg += '</svg>';
        return svg;
    }
}

// 全局渲染函数
window.renderMermaidLocally = function(code) {
    const renderer = new SimpleMermaidRenderer();
    return renderer.parseMermaidCode(code);
};

console.log('本地Mermaid渲染器已加载');
